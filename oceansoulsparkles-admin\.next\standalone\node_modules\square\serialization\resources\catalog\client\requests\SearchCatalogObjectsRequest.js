"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchCatalogObjectsRequest = void 0;
const core = __importStar(require("../../../../../core"));
const CatalogObjectType_1 = require("../../../../types/CatalogObjectType");
const CatalogQuery_1 = require("../../../../types/CatalogQuery");
exports.SearchCatalogObjectsRequest = core.serialization.object({
    cursor: core.serialization.string().optional(),
    objectTypes: core.serialization.property("object_types", core.serialization.list(CatalogObjectType_1.CatalogObjectType).optional()),
    includeDeletedObjects: core.serialization.property("include_deleted_objects", core.serialization.boolean().optional()),
    includeRelatedObjects: core.serialization.property("include_related_objects", core.serialization.boolean().optional()),
    beginTime: core.serialization.property("begin_time", core.serialization.string().optional()),
    query: CatalogQuery_1.CatalogQuery.optional(),
    limit: core.serialization.number().optional(),
    includeCategoryPathToRoot: core.serialization.property("include_category_path_to_root", core.serialization.boolean().optional()),
});
