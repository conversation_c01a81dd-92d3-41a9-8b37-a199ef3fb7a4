<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:0.4" />
    </linearGradient>
  </defs>
  <rect width="800" height="600" fill="url(#grad)"/>
  <rect x="50" y="50" width="700" height="500" fill="none" stroke="white" stroke-width="2" stroke-dasharray="10,5" opacity="0.6"/>
  <text x="400" y="280" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">
    Ocean Soul Sparkles
  </text>
  <text x="400" y="320" font-family="Arial, sans-serif" font-size="18" text-anchor="middle" fill="white" opacity="0.9">
    Glitter <PERSON>el <PERSON>
  </text>
  <text x="400" y="360" font-family="Arial, sans-serif" font-size="14" text-anchor="middle" fill="white" opacity="0.7">
    Placeholder Image
  </text>
  <circle cx="400" cy="450" r="40" fill="white" opacity="0.3"/>
  <text x="400" y="460" font-family="Arial, sans-serif" font-size="36" text-anchor="middle" fill="white">
    ✨
  </text>
</svg>
