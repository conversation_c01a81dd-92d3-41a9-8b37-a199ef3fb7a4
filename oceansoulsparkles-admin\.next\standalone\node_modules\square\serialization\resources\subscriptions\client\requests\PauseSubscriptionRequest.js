"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PauseSubscriptionRequest = void 0;
const core = __importStar(require("../../../../../core"));
const ChangeTiming_1 = require("../../../../types/ChangeTiming");
exports.PauseSubscriptionRequest = core.serialization.object({
    pauseEffectiveDate: core.serialization.property("pause_effective_date", core.serialization.string().optionalNullable()),
    pauseCycleDuration: core.serialization.property("pause_cycle_duration", core.serialization.bigint().optionalNullable()),
    resumeEffectiveDate: core.serialization.property("resume_effective_date", core.serialization.string().optionalNullable()),
    resumeChangeTiming: core.serialization.property("resume_change_timing", ChangeTiming_1.ChangeTiming.optional()),
    pauseReason: core.serialization.property("pause_reason", core.serialization.string().optionalNullable()),
});
