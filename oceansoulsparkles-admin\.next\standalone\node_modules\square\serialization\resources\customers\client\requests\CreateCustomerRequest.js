"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCustomerRequest = void 0;
const core = __importStar(require("../../../../../core"));
const Address_1 = require("../../../../types/Address");
const CustomerTaxIds_1 = require("../../../../types/CustomerTaxIds");
exports.CreateCustomerRequest = core.serialization.object({
    idempotencyKey: core.serialization.property("idempotency_key", core.serialization.string().optional()),
    givenName: core.serialization.property("given_name", core.serialization.string().optional()),
    familyName: core.serialization.property("family_name", core.serialization.string().optional()),
    companyName: core.serialization.property("company_name", core.serialization.string().optional()),
    nickname: core.serialization.string().optional(),
    emailAddress: core.serialization.property("email_address", core.serialization.string().optional()),
    address: Address_1.Address.optional(),
    phoneNumber: core.serialization.property("phone_number", core.serialization.string().optional()),
    referenceId: core.serialization.property("reference_id", core.serialization.string().optional()),
    note: core.serialization.string().optional(),
    birthday: core.serialization.string().optional(),
    taxIds: core.serialization.property("tax_ids", CustomerTaxIds_1.CustomerTaxIds.optional()),
});
