"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateCheckoutRequest = void 0;
const core = __importStar(require("../../../../../core"));
const CreateOrderRequest_1 = require("../../../../types/CreateOrderRequest");
const Address_1 = require("../../../../types/Address");
const ChargeRequestAdditionalRecipient_1 = require("../../../../types/ChargeRequestAdditionalRecipient");
exports.CreateCheckoutRequest = core.serialization.object({
    idempotencyKey: core.serialization.property("idempotency_key", core.serialization.string()),
    order: CreateOrderRequest_1.CreateOrderRequest,
    askForShippingAddress: core.serialization.property("ask_for_shipping_address", core.serialization.boolean().optional()),
    merchantSupportEmail: core.serialization.property("merchant_support_email", core.serialization.string().optional()),
    prePopulateBuyerEmail: core.serialization.property("pre_populate_buyer_email", core.serialization.string().optional()),
    prePopulateShippingAddress: core.serialization.property("pre_populate_shipping_address", Address_1.Address.optional()),
    redirectUrl: core.serialization.property("redirect_url", core.serialization.string().optional()),
    additionalRecipients: core.serialization.property("additional_recipients", core.serialization.list(ChargeRequestAdditionalRecipient_1.ChargeRequestAdditionalRecipient).optional()),
    note: core.serialization.string().optional(),
});
