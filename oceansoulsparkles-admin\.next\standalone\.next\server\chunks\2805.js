"use strict";exports.id=2805,exports.ids=[2805],exports.modules={8781:(e,a)=>{Object.defineProperty(a,"l",{enumerable:!0,get:function(){return function e(a,t){return t in a?a[t]:"then"in a&&"function"==typeof a.then?a.then(a=>e(a,t)):"function"==typeof a&&"default"===t?a:void 0}}})},7474:(e,a,t)=>{t.d(a,{$g:()=>I,rE:()=>A,ZQ:()=>E,Wg:()=>p,cm:()=>v});var r=t(9344),i=t.n(r),s=t(8432),n=t.n(s),o=t(2885);let c=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder",l=(0,o.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",c);async function d(e){try{let a={action:e.action,user_id:e.userId,user_role:e.userRole,email:e.email,ip_address:e.ip,path:e.path,resource:e.resource,resource_id:e.resourceId,old_values:e.oldValues,new_values:e.newValues,reason:e.reason,error:e.error,metadata:e.metadata,severity:e.severity||"medium",created_at:new Date().toISOString()},{error:t}=await l.from("audit_logs").insert(a);t&&console.error("Failed to write audit log to database:",t);let r=function(e){switch(e){case"low":case"medium":default:return"log";case"high":return"warn";case"critical":return"error"}}(e.severity||"medium"),i=function(e){let a=new Date().toISOString(),t=e.userId?`[User: ${e.userId}]`:"",r=e.ip?`[IP: ${e.ip}]`:"",i=e.path?`[Path: ${e.path}]`:"";return`[AUDIT] ${a} ${e.action} ${t} ${r} ${i} ${e.reason||""}`.trim()}(e);console[r](i),"critical"===e.severity&&await u(e)}catch(a){console.error("Audit logging failed:",a),console.error("AUDIT_LOG_FAILURE:",JSON.stringify(e,null,2))}}async function u(e){try{console.error("\uD83D\uDEA8 CRITICAL SECURITY EVENT:",{action:e.action,userId:e.userId,ip:e.ip,reason:e.reason,timestamp:new Date().toISOString()}),"true"===process.env.ENABLE_CRITICAL_ALERTS&&await m(e)}catch(e){console.error("Failed to send critical alert:",e)}}async function m(e){console.log("Email alert would be sent for:",e.action)}let _=process.env.SUPABASE_SERVICE_ROLE_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.placeholder",f=(0,o.createClient)("https://ndlgbcsbidyhxbpqzgqp.supabase.co",_);async function p(e){try{let a=process.env.JWT_SECRET||"placeholder-secret",t=i().verify(e,a),{data:r,error:s}=await f.from("admin_users").select(`
        id,
        email,
        role,
        first_name,
        last_name,
        is_active,
        mfa_enabled,
        last_activity,
        permissions
      `).eq("id",t.userId).eq("is_active",!0).single();if(s||!r)return{valid:!1,error:"User not found or inactive"};if(!r.is_active)return{valid:!1,error:"User account is deactivated"};return await f.from("admin_users").update({last_activity:Date.now()}).eq("id",r.id),{valid:!0,user:{id:r.id,email:r.email,role:r.role,firstName:r.first_name,lastName:r.last_name,isActive:r.is_active,mfaEnabled:r.mfa_enabled,lastActivity:Date.now(),permissions:r.permissions||[]}}}catch(e){return{valid:!1,error:"Invalid token"}}}async function I(e,a,t){try{let{data:r}=await f.from("login_attempts").select("*").eq("email",e).gte("created_at",new Date(Date.now()-9e5).toISOString()).order("created_at",{ascending:!1});if(r&&r.length>=5)return await d({action:"LOGIN_BLOCKED",email:e,ip:t,reason:"Too many failed attempts"}),{success:!1,error:"Account temporarily locked due to too many failed attempts"};let{data:s,error:o}=await f.from("admin_users").select(`
        id,
        email,
        password_hash,
        role,
        first_name,
        last_name,
        is_active,
        mfa_enabled,
        mfa_secret,
        permissions
      `).eq("email",e.toLowerCase()).single();if(o||!s)return await w(e,t,o?`Database error: ${o.message}`:"User not found"),{success:!1,error:"Invalid credentials"};if(!s.is_active)return await d({action:"LOGIN_DENIED",userId:s.id,email:e,ip:t,reason:"Account deactivated"}),{success:!1,error:"Account is deactivated"};if(!await n().compare(a,s.password_hash))return await w(e,t,"Invalid password"),{success:!1,error:"Invalid credentials"};if(await f.from("login_attempts").delete().eq("email",e),s.mfa_enabled&&s.mfa_secret)return{success:!0,requiresMFA:!0,user:{id:s.id,email:s.email,role:s.role,firstName:s.first_name,lastName:s.last_name,isActive:s.is_active,mfaEnabled:s.mfa_enabled,lastActivity:Date.now(),permissions:s.permissions||[]}};let c=process.env.JWT_SECRET||"placeholder-secret",l=i().sign({userId:s.id,email:s.email,role:s.role},c,{expiresIn:"8h"});return await f.from("admin_users").update({last_login_at:new Date().toISOString(),last_activity:Date.now()}).eq("id",s.id),await d({action:"LOGIN_SUCCESS",userId:s.id,userRole:s.role,email:e,ip:t}),{success:!0,token:l,user:{id:s.id,email:s.email,role:s.role,firstName:s.first_name,lastName:s.last_name,isActive:s.is_active,mfaEnabled:s.mfa_enabled,lastActivity:Date.now(),permissions:s.permissions||[]}}}catch(e){return console.error("Admin login error:",e),{success:!1,error:"Login failed"}}}async function v(e,a,r){let s=await Promise.resolve().then(t.t.bind(t,9200,23));try{let{data:t,error:n}=await f.from("admin_users").select(`
        id,
        email,
        role,
        first_name,
        last_name,
        is_active,
        mfa_enabled,
        mfa_secret,
        permissions
      `).eq("id",e).single();if(n||!t||!t.mfa_secret)return{success:!1,error:"Invalid MFA setup"};if(!s.totp.verify({secret:t.mfa_secret,encoding:"base32",token:a,window:2}))return await d({action:"MFA_FAILED",userId:t.id,email:t.email,ip:r,reason:"Invalid MFA token"}),{success:!1,error:"Invalid MFA token"};let o=process.env.JWT_SECRET||"placeholder-secret",c=i().sign({userId:t.id,email:t.email,role:t.role},o,{expiresIn:"8h"});return await f.from("admin_users").update({last_login_at:new Date().toISOString(),last_activity:Date.now()}).eq("id",t.id),await d({action:"MFA_LOGIN_SUCCESS",userId:t.id,userRole:t.role,email:t.email,ip:r}),{success:!0,token:c,user:{id:t.id,email:t.email,role:t.role,firstName:t.first_name,lastName:t.last_name,isActive:t.is_active,mfaEnabled:t.mfa_enabled,lastActivity:Date.now(),permissions:t.permissions||[]}}}catch(e){return console.error("MFA verification error:",e),{success:!1,error:"MFA verification failed"}}}async function w(e,a,t){await f.from("login_attempts").insert({email:e,ip_address:a,success:!1,reason:t,created_at:new Date().toISOString()}),await d({action:"LOGIN_FAILED",email:e,ip:a,reason:t})}async function A(e,a){try{await d({action:"LOGOUT",userId:e,ip:a})}catch(e){console.error("Logout audit error:",e)}}async function E(e){return p(e)}},7153:(e,a)=>{var t;Object.defineProperty(a,"x",{enumerable:!0,get:function(){return t}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(t||(t={}))},1802:(e,a,t)=>{e.exports=t(1287)}};