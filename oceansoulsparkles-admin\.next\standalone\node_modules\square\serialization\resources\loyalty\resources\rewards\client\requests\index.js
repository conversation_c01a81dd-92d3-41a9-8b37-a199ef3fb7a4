"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedeemLoyaltyRewardRequest = exports.SearchLoyaltyRewardsRequest = exports.CreateLoyaltyRewardRequest = void 0;
var CreateLoyaltyRewardRequest_1 = require("./CreateLoyaltyRewardRequest");
Object.defineProperty(exports, "CreateLoyaltyRewardRequest", { enumerable: true, get: function () { return CreateLoyaltyRewardRequest_1.CreateLoyaltyRewardRequest; } });
var SearchLoyaltyRewardsRequest_1 = require("./SearchLoyaltyRewardsRequest");
Object.defineProperty(exports, "SearchLoyaltyRewardsRequest", { enumerable: true, get: function () { return SearchLoyaltyRewardsRequest_1.SearchLoyaltyRewardsRequest; } });
var RedeemLoyaltyRewardRequest_1 = require("./RedeemLoyaltyRewardRequest");
Object.defineProperty(exports, "RedeemLoyaltyRewardRequest", { enumerable: true, get: function () { return RedeemLoyaltyRewardRequest_1.RedeemLoyaltyRewardRequest; } });
