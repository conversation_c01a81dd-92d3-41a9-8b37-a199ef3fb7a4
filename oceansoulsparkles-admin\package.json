{"name": "oceansoulsparkles-admin", "version": "1.0.0", "description": "Ocean Soul Sparkles Admin Subdomain - Staff Portal and Management System", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "node scripts/check-env.js && next build", "start": "next start -p 3002", "lint": "next lint", "type-check": "tsc --noEmit", "security-audit": "node scripts/security-audit.js", "deploy-admin": "node scripts/deploy-admin.js", "create-dev-user": "node scripts/create-dev-user.js", "dev-setup": "node scripts/create-dev-user.js && npm run dev", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@supabase/supabase-js": "^2.45.4", "@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^22.7.5", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/speakeasy": "^2.0.10", "bcryptjs": "^2.4.3", "crypto-js": "^4.2.0", "dotenv": "^16.5.0", "jose": "^6.0.11", "jsonwebtoken": "^9.0.2", "next": "14.2.30", "node-fetch": "^3.3.2", "nodemailer": "^6.9.8", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-toastify": "^9.1.3", "speakeasy": "^2.0.0", "square": "^42.3.0", "squareup": "^1.0.0", "typescript": "^5.6.3"}, "devDependencies": {"@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@typescript-eslint/eslint-plugin": "^8.8.1", "@typescript-eslint/parser": "^8.8.1", "eslint": "^8.57.1", "eslint-config-next": "14.2.30", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["ocean-soul-sparkles", "admin", "staff-portal", "booking-management", "artist-portal", "next.js", "supabase"], "author": "Ocean Soul Sparkles", "license": "UNLICENSED"}