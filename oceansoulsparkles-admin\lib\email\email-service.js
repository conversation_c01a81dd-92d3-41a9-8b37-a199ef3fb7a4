/**
 * Email Service for Ocean Soul Sparkles Admin
 * Main service that combines SMTP functionality with email templates
 */

const { sendEmail, verifyConnection, sendTestEmail } = require('./smtp-service');
const {
  bookingConfirmationTemplate,
  bookingReminderTemplate,
  bookingCancellationTemplate,
  paymentReceiptTemplate,
  staffNotificationTemplate,
  lowInventoryAlertTemplate
} = require('./templates');

/**
 * Email service class
 */
class EmailService {
  constructor() {
    this.isConfigured = !!(process.env.SMTP_USER && process.env.SMTP_PASS);
  }

  /**
   * Send booking confirmation email
   */
  async sendBookingConfirmation(booking) {
    if (!booking.customerEmail) {
      console.warn('No customer email provided for booking confirmation');
      return { success: false, error: 'No customer email' };
    }

    const html = bookingConfirmationTemplate(booking);
    
    return await sendEmail({
      to: booking.customerEmail,
      subject: `Booking Confirmation - ${booking.serviceName}`,
      html
    });
  }

  /**
   * Send booking reminder email
   */
  async sendBookingReminder(booking) {
    if (!booking.customerEmail) {
      console.warn('No customer email provided for booking reminder');
      return { success: false, error: 'No customer email' };
    }

    const html = bookingReminderTemplate(booking);
    
    return await sendEmail({
      to: booking.customerEmail,
      subject: `Appointment Reminder - Tomorrow at ${booking.time}`,
      html
    });
  }

  /**
   * Send booking cancellation email
   */
  async sendBookingCancellation(booking) {
    if (!booking.customerEmail) {
      console.warn('No customer email provided for booking cancellation');
      return { success: false, error: 'No customer email' };
    }

    const html = bookingCancellationTemplate(booking);
    
    return await sendEmail({
      to: booking.customerEmail,
      subject: `Booking Cancellation - ${booking.serviceName}`,
      html
    });
  }

  /**
   * Send payment receipt email
   */
  async sendPaymentReceipt(payment) {
    if (!payment.customerEmail) {
      console.warn('No customer email provided for payment receipt');
      return { success: false, error: 'No customer email' };
    }

    const html = paymentReceiptTemplate(payment);
    
    return await sendEmail({
      to: payment.customerEmail,
      subject: `Payment Receipt - ${payment.receiptNumber}`,
      html
    });
  }

  /**
   * Send staff notification email
   */
  async sendStaffNotification(notification) {
    if (!notification.staffEmail) {
      console.warn('No staff email provided for notification');
      return { success: false, error: 'No staff email' };
    }

    const html = staffNotificationTemplate(notification);
    
    return await sendEmail({
      to: notification.staffEmail,
      subject: notification.subject || 'Staff Notification',
      html
    });
  }

  /**
   * Send low inventory alert to admin
   */
  async sendLowInventoryAlert(items, adminEmail) {
    if (!adminEmail) {
      adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    }

    const html = lowInventoryAlertTemplate(items);
    
    return await sendEmail({
      to: adminEmail,
      subject: `Low Inventory Alert - ${items.length} items need attention`,
      html
    });
  }

  /**
   * Send bulk emails (for newsletters, announcements, etc.)
   */
  async sendBulkEmail(recipients, subject, html) {
    const results = [];
    
    for (const recipient of recipients) {
      try {
        const result = await sendEmail({
          to: recipient.email,
          subject,
          html: html.replace(/{{name}}/g, recipient.name || 'Valued Customer')
        });
        results.push({ email: recipient.email, ...result });
        
        // Add delay to avoid overwhelming SMTP server
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        results.push({ 
          email: recipient.email, 
          success: false, 
          error: error.message 
        });
      }
    }
    
    return results;
  }

  /**
   * Verify email configuration
   */
  async verifyConfiguration() {
    return await verifyConnection();
  }

  /**
   * Send test email
   */
  async sendTest(to) {
    return await sendTestEmail(to);
  }

  /**
   * Get email service status
   */
  getStatus() {
    return {
      configured: this.isConfigured,
      smtpHost: process.env.SMTP_HOST || 'Not configured',
      smtpUser: process.env.SMTP_USER ? 'Configured' : 'Not configured',
      smtpPort: process.env.SMTP_PORT || '587'
    };
  }
}

// Create singleton instance
const emailService = new EmailService();

module.exports = emailService;
