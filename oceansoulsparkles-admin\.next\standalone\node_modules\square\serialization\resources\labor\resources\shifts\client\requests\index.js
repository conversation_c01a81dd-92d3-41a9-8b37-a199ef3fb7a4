"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateShiftRequest = exports.SearchShiftsRequest = exports.CreateShiftRequest = void 0;
var CreateShiftRequest_1 = require("./CreateShiftRequest");
Object.defineProperty(exports, "CreateShiftRequest", { enumerable: true, get: function () { return CreateShiftRequest_1.CreateShiftRequest; } });
var SearchShiftsRequest_1 = require("./SearchShiftsRequest");
Object.defineProperty(exports, "SearchShiftsRequest", { enumerable: true, get: function () { return SearchShiftsRequest_1.SearchShiftsRequest; } });
var UpdateShiftRequest_1 = require("./UpdateShiftRequest");
Object.defineProperty(exports, "UpdateShiftRequest", { enumerable: true, get: function () { return UpdateShiftRequest_1.UpdateShiftRequest; } });
