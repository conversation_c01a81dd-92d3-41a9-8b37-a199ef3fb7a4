"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObtainTokenRequest = exports.RevokeTokenRequest = void 0;
var RevokeTokenRequest_1 = require("./RevokeTokenRequest");
Object.defineProperty(exports, "RevokeTokenRequest", { enumerable: true, get: function () { return RevokeTokenRequest_1.RevokeTokenRequest; } });
var ObtainTokenRequest_1 = require("./ObtainTokenRequest");
Object.defineProperty(exports, "ObtainTokenRequest", { enumerable: true, get: function () { return ObtainTokenRequest_1.ObtainTokenRequest; } });
