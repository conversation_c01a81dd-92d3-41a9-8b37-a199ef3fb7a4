import { supabaseAdmin } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/auth/admin-auth'

/**
 * API endpoint for managing receipt templates
 * Handles CRUD operations for receipt customization
 */
export default async function handler(req, res) {
  try {
    // Authenticate admin user
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.success) {
      return res.status(401).json({ error: authResult.error })
    }

    const { user } = authResult
    const requestId = Math.random().toString(36).substring(2, 8)
    console.log(`[${requestId}] Receipt templates API called by ${user.email}`)

    switch (req.method) {
      case 'GET':
        return await handleGetTemplates(req, res, user, requestId)
      case 'POST':
        return await handleCreateTemplate(req, res, user, requestId)
      case 'PUT':
        return await handleUpdateTemplate(req, res, user, requestId)
      case 'DELETE':
        return await handleDeleteTemplate(req, res, user, requestId)
      default:
        return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('Receipt templates API error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}

/**
 * Get all receipt templates
 */
async function handleGetTemplates(req, res, user, requestId) {
  try {
    const { data: templates, error } = await supabaseAdmin
      .from('receipt_templates')
      .select('*')
      .eq('is_active', true)
      .order('is_default', { ascending: false })
      .order('created_at', { ascending: false })

    if (error) {
      console.error(`[${requestId}] Error fetching receipt templates:`, error)
      return res.status(500).json({ error: 'Failed to fetch receipt templates' })
    }

    console.log(`[${requestId}] Retrieved ${templates?.length || 0} receipt templates`)
    return res.status(200).json({ templates: templates || [] })
  } catch (error) {
    console.error(`[${requestId}] Error in handleGetTemplates:`, error)
    return res.status(500).json({ error: 'Failed to fetch receipt templates' })
  }
}

/**
 * Create new receipt template
 */
async function handleCreateTemplate(req, res, user, requestId) {
  try {
    const {
      name,
      description,
      template_type,
      is_default,
      business_name,
      business_address,
      business_phone,
      business_email,
      business_website,
      business_abn,
      show_logo,
      logo_position,
      header_color,
      text_color,
      font_family,
      font_size,
      show_customer_details,
      show_service_details,
      show_artist_details,
      show_payment_details,
      show_booking_notes,
      show_terms_conditions,
      footer_message,
      show_social_media,
      social_media_links,
      custom_fields
    } = req.body

    if (!name || !template_type) {
      return res.status(400).json({ error: 'Name and template type are required' })
    }

    // If setting as default, unset other defaults first
    if (is_default) {
      await supabaseAdmin
        .from('receipt_templates')
        .update({ is_default: false })
        .eq('is_default', true)
    }

    const { data: template, error } = await supabaseAdmin
      .from('receipt_templates')
      .insert([{
        name,
        description,
        template_type,
        is_default: is_default || false,
        is_active: true,
        business_name,
        business_address,
        business_phone,
        business_email,
        business_website,
        business_abn,
        show_logo,
        logo_position,
        header_color,
        text_color,
        font_family,
        font_size,
        show_customer_details,
        show_service_details,
        show_artist_details,
        show_payment_details,
        show_booking_notes,
        show_terms_conditions,
        footer_message,
        show_social_media,
        social_media_links,
        custom_fields,
        created_by: user.id,
        updated_by: user.id
      }])
      .select()
      .single()

    if (error) {
      console.error(`[${requestId}] Error creating receipt template:`, error)
      return res.status(500).json({ error: 'Failed to create receipt template' })
    }

    console.log(`[${requestId}] Created receipt template: ${template.name}`)
    return res.status(201).json({ template })
  } catch (error) {
    console.error(`[${requestId}] Error in handleCreateTemplate:`, error)
    return res.status(500).json({ error: 'Failed to create receipt template' })
  }
}

/**
 * Update existing receipt template
 */
async function handleUpdateTemplate(req, res, user, requestId) {
  try {
    const { id } = req.query
    if (!id) {
      return res.status(400).json({ error: 'Template ID is required' })
    }

    const updateData = { ...req.body }
    delete updateData.id
    updateData.updated_by = user.id
    updateData.updated_at = new Date().toISOString()

    // If setting as default, unset other defaults first
    if (updateData.is_default) {
      await supabaseAdmin
        .from('receipt_templates')
        .update({ is_default: false })
        .eq('is_default', true)
        .neq('id', id)
    }

    const { data: template, error } = await supabaseAdmin
      .from('receipt_templates')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error(`[${requestId}] Error updating receipt template:`, error)
      return res.status(500).json({ error: 'Failed to update receipt template' })
    }

    console.log(`[${requestId}] Updated receipt template: ${template.name}`)
    return res.status(200).json({ template })
  } catch (error) {
    console.error(`[${requestId}] Error in handleUpdateTemplate:`, error)
    return res.status(500).json({ error: 'Failed to update receipt template' })
  }
}

/**
 * Delete receipt template (soft delete)
 */
async function handleDeleteTemplate(req, res, user, requestId) {
  try {
    const { id } = req.query
    if (!id) {
      return res.status(400).json({ error: 'Template ID is required' })
    }

    // Check if it's the default template
    const { data: template } = await supabaseAdmin
      .from('receipt_templates')
      .select('is_default, name')
      .eq('id', id)
      .single()

    if (template?.is_default) {
      return res.status(400).json({ error: 'Cannot delete the default template' })
    }

    const { error } = await supabaseAdmin
      .from('receipt_templates')
      .update({ 
        is_active: false,
        updated_by: user.id,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)

    if (error) {
      console.error(`[${requestId}] Error deleting receipt template:`, error)
      return res.status(500).json({ error: 'Failed to delete receipt template' })
    }

    console.log(`[${requestId}] Deleted receipt template: ${template?.name}`)
    return res.status(200).json({ success: true })
  } catch (error) {
    console.error(`[${requestId}] Error in handleDeleteTemplate:`, error)
    return res.status(500).json({ error: 'Failed to delete receipt template' })
  }
}
