/**
 * Receipt Generator for Ocean Soul Sparkles
 * Generates customized receipts based on templates and booking data
 */

import { supabaseAdmin } from './supabase'

/**
 * Generate receipt HTML based on template and booking data
 */
export async function generateReceipt(bookingData, templateId = null) {
  try {
    // Get receipt template
    const template = await getReceiptTemplate(templateId)
    if (!template) {
      throw new Error('Receipt template not found')
    }

    // Generate receipt HTML
    const receiptHtml = buildReceiptHtml(template, bookingData)
    
    return {
      success: true,
      html: receiptHtml,
      template: template
    }
  } catch (error) {
    console.error('Error generating receipt:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * Get receipt template by ID or default template
 */
async function getReceiptTemplate(templateId = null) {
  try {
    let query = supabaseAdmin
      .from('receipt_templates')
      .select('*')
      .eq('is_active', true)

    if (templateId) {
      query = query.eq('id', templateId)
    } else {
      query = query.eq('is_default', true)
    }

    const { data: templates, error } = await query.limit(1)

    if (error) {
      // If table doesn't exist, return default template
      if (error.code === '42P01') {
        console.log('Receipt templates table not found, using default template')
        return getDefaultTemplate()
      }
      throw new Error(`Database error: ${error.message}`)
    }

    if (!templates || templates.length === 0) {
      // Fallback to any active template
      const { data: fallbackTemplates } = await supabaseAdmin
        .from('receipt_templates')
        .select('*')
        .eq('is_active', true)
        .limit(1)

      return fallbackTemplates?.[0] || getDefaultTemplate()
    }

    return templates[0]
  } catch (error) {
    console.error('Error fetching receipt template:', error)
    return getDefaultTemplate()
  }
}

/**
 * Get default template when database is not available
 */
function getDefaultTemplate() {
  return {
    id: 'default-standard',
    name: 'Standard Receipt',
    description: 'Default receipt template',
    template_type: 'standard',
    is_default: true,
    is_active: true,
    business_name: 'Ocean Soul Sparkles',
    business_address: 'Australia',
    business_phone: '+61 XXX XXX XXX',
    business_email: '<EMAIL>',
    business_website: 'oceansoulsparkles.com.au',
    business_abn: '',
    show_logo: true,
    logo_position: 'center',
    header_color: '#667eea',
    text_color: '#333333',
    font_family: 'Arial',
    font_size: 12,
    show_customer_details: true,
    show_service_details: true,
    show_artist_details: true,
    show_payment_details: true,
    show_booking_notes: false,
    show_terms_conditions: true,
    footer_message: 'Thank you for choosing Ocean Soul Sparkles!',
    show_social_media: false,
    social_media_links: null,
    custom_fields: []
  }
}

/**
 * Build receipt HTML from template and booking data
 */
function buildReceiptHtml(template, booking) {
  const {
    business_name,
    business_address,
    business_phone,
    business_email,
    business_website,
    business_abn,
    show_logo,
    logo_position,
    header_color,
    text_color,
    font_family,
    font_size,
    show_customer_details,
    show_service_details,
    show_artist_details,
    show_payment_details,
    show_booking_notes,
    show_terms_conditions,
    footer_message,
    show_social_media,
    social_media_links,
    template_type
  } = template

  // Generate receipt number if not provided
  const receiptNumber = booking.receipt_number || `OSS-${Date.now()}`
  const receiptDate = new Date().toLocaleDateString('en-AU', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })

  // Build CSS styles
  const styles = `
    <style>
      body { 
        font-family: ${font_family}, sans-serif; 
        font-size: ${font_size}px; 
        color: ${text_color}; 
        margin: 0; 
        padding: 20px; 
        line-height: 1.4;
        max-width: 400px;
        margin: 0 auto;
      }
      .receipt-header { 
        text-align: ${logo_position}; 
        margin-bottom: 20px; 
        padding-bottom: 15px;
        border-bottom: 2px solid ${header_color};
      }
      .business-name { 
        font-size: ${Math.round(font_size * 1.5)}px; 
        font-weight: bold; 
        color: ${header_color}; 
        margin: 0 0 5px 0;
      }
      .business-info { 
        font-size: ${Math.round(font_size * 0.9)}px; 
        color: #666; 
        margin: 2px 0;
      }
      .receipt-title { 
        font-size: ${Math.round(font_size * 1.2)}px; 
        font-weight: bold; 
        text-align: center; 
        margin: 20px 0 15px 0;
        text-transform: uppercase;
        letter-spacing: 1px;
      }
      .receipt-info { 
        margin-bottom: 20px; 
        padding: 10px 0;
        border-bottom: 1px solid #eee;
      }
      .section { 
        margin-bottom: 15px; 
      }
      .section-title { 
        font-weight: bold; 
        margin-bottom: 8px; 
        color: ${header_color};
        font-size: ${Math.round(font_size * 1.1)}px;
      }
      .detail-row { 
        display: flex; 
        justify-content: space-between; 
        margin-bottom: 3px;
        align-items: flex-start;
      }
      .detail-label { 
        font-weight: 500; 
        flex: 1;
      }
      .detail-value { 
        text-align: right; 
        flex: 1;
        word-break: break-word;
      }
      .total-section { 
        border-top: 2px solid ${header_color}; 
        padding-top: 10px; 
        margin-top: 15px;
      }
      .total-row { 
        display: flex; 
        justify-content: space-between; 
        font-weight: bold; 
        font-size: ${Math.round(font_size * 1.1)}px;
        margin-bottom: 5px;
      }
      .footer { 
        text-align: center; 
        margin-top: 20px; 
        padding-top: 15px; 
        border-top: 1px solid #eee;
        font-size: ${Math.round(font_size * 0.9)}px;
      }
      .footer-message { 
        font-style: italic; 
        color: #666; 
        margin-bottom: 10px;
      }
      .compact { font-size: ${Math.round(font_size * 0.9)}px; }
      .detailed { font-size: ${font_size}px; }
      @media print {
        body { margin: 0; padding: 10px; }
        .receipt-header { page-break-inside: avoid; }
      }
    </style>
  `

  // Build header section
  let headerHtml = `
    <div class="receipt-header">
      ${show_logo ? `<div class="business-name">${business_name}</div>` : ''}
      ${business_address ? `<div class="business-info">${business_address}</div>` : ''}
      ${business_phone ? `<div class="business-info">${business_phone}</div>` : ''}
      ${business_email ? `<div class="business-info">${business_email}</div>` : ''}
      ${business_website ? `<div class="business-info">${business_website}</div>` : ''}
      ${business_abn ? `<div class="business-info">ABN: ${business_abn}</div>` : ''}
    </div>
  `

  // Build receipt info section
  let receiptInfoHtml = `
    <div class="receipt-title">Receipt</div>
    <div class="receipt-info">
      <div class="detail-row">
        <span class="detail-label">Receipt #:</span>
        <span class="detail-value">${receiptNumber}</span>
      </div>
      <div class="detail-row">
        <span class="detail-label">Date:</span>
        <span class="detail-value">${receiptDate}</span>
      </div>
    </div>
  `

  // Build customer details section
  let customerHtml = ''
  if (show_customer_details && booking.customer_name) {
    customerHtml = `
      <div class="section">
        <div class="section-title">Customer Details</div>
        <div class="detail-row">
          <span class="detail-label">Name:</span>
          <span class="detail-value">${booking.customer_name}</span>
        </div>
        ${booking.customer_email ? `
        <div class="detail-row">
          <span class="detail-label">Email:</span>
          <span class="detail-value">${booking.customer_email}</span>
        </div>` : ''}
        ${booking.customer_phone ? `
        <div class="detail-row">
          <span class="detail-label">Phone:</span>
          <span class="detail-value">${booking.customer_phone}</span>
        </div>` : ''}
      </div>
    `
  }

  // Build service details section
  let serviceHtml = ''
  if (show_service_details) {
    const startTime = booking.start_time ? new Date(booking.start_time).toLocaleString('en-AU') : 'N/A'
    const duration = booking.duration ? `${booking.duration} minutes` : 'N/A'
    
    serviceHtml = `
      <div class="section">
        <div class="section-title">Service Details</div>
        <div class="detail-row">
          <span class="detail-label">Service:</span>
          <span class="detail-value">${booking.service_name || 'N/A'}</span>
        </div>
        ${booking.tier_name ? `
        <div class="detail-row">
          <span class="detail-label">Tier:</span>
          <span class="detail-value">${booking.tier_name}</span>
        </div>` : ''}
        <div class="detail-row">
          <span class="detail-label">Date & Time:</span>
          <span class="detail-value">${startTime}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Duration:</span>
          <span class="detail-value">${duration}</span>
        </div>
      </div>
    `
  }

  // Build artist details section
  let artistHtml = ''
  if (show_artist_details && booking.artist_name) {
    artistHtml = `
      <div class="section">
        <div class="section-title">Artist Details</div>
        <div class="detail-row">
          <span class="detail-label">Artist:</span>
          <span class="detail-value">${booking.artist_name}</span>
        </div>
      </div>
    `
  }

  // Build payment details section
  let paymentHtml = ''
  if (show_payment_details) {
    const totalAmount = booking.total_amount || 0
    const tipAmount = booking.tip_amount || 0
    const subtotal = totalAmount - tipAmount
    
    paymentHtml = `
      <div class="section">
        <div class="section-title">Payment Details</div>
        ${tipAmount > 0 ? `
        <div class="detail-row">
          <span class="detail-label">Subtotal:</span>
          <span class="detail-value">$${subtotal.toFixed(2)}</span>
        </div>
        <div class="detail-row">
          <span class="detail-label">Tip:</span>
          <span class="detail-value">$${tipAmount.toFixed(2)}</span>
        </div>` : ''}
        <div class="total-section">
          <div class="total-row">
            <span>Total:</span>
            <span>$${totalAmount.toFixed(2)}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Payment Method:</span>
            <span class="detail-value">${booking.payment_method || 'N/A'}</span>
          </div>
          <div class="detail-row">
            <span class="detail-label">Status:</span>
            <span class="detail-value">Paid</span>
          </div>
        </div>
      </div>
    `
  }

  // Build notes section
  let notesHtml = ''
  if (show_booking_notes && booking.notes) {
    notesHtml = `
      <div class="section">
        <div class="section-title">Notes</div>
        <div>${booking.notes}</div>
      </div>
    `
  }

  // Build footer section
  let footerHtml = ''
  if (footer_message || show_terms_conditions) {
    footerHtml = `
      <div class="footer">
        ${footer_message ? `<div class="footer-message">${footer_message}</div>` : ''}
        ${show_terms_conditions ? `
        <div style="font-size: ${Math.round(font_size * 0.8)}px; color: #888;">
          Terms & Conditions apply. Visit our website for details.
        </div>` : ''}
        ${show_social_media && social_media_links ? `
        <div style="margin-top: 10px;">
          Follow us on social media for updates and inspiration!
        </div>` : ''}
      </div>
    `
  }

  // Combine all sections
  const receiptHtml = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Receipt - ${receiptNumber}</title>
      ${styles}
    </head>
    <body class="${template_type}">
      ${headerHtml}
      ${receiptInfoHtml}
      ${customerHtml}
      ${serviceHtml}
      ${artistHtml}
      ${paymentHtml}
      ${notesHtml}
      ${footerHtml}
    </body>
    </html>
  `

  return receiptHtml
}

/**
 * Generate receipt for POS transaction
 */
export async function generatePOSReceipt(transactionData, templateId = null) {
  const bookingData = {
    receipt_number: transactionData.receiptNumber || `POS-${Date.now()}`,
    customer_name: transactionData.customerName || 'Walk-in Customer',
    customer_email: transactionData.customerEmail,
    customer_phone: transactionData.customerPhone,
    service_name: transactionData.serviceName,
    tier_name: transactionData.tierName,
    artist_name: transactionData.artistName,
    start_time: transactionData.startTime || new Date().toISOString(),
    duration: transactionData.duration,
    total_amount: transactionData.totalAmount,
    tip_amount: transactionData.tipAmount || 0,
    payment_method: transactionData.paymentMethod,
    notes: transactionData.notes
  }

  return await generateReceipt(bookingData, templateId)
}
