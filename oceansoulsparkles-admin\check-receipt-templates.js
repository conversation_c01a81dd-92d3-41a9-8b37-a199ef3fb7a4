require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupReceiptTemplates() {
  try {
    console.log('🔧 Setting up receipt templates...');
    
    // Create receipt templates table
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS receipt_templates (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name VARCHAR(100) NOT NULL,
        description TEXT,
        template_type VARCHAR(50) NOT NULL DEFAULT 'standard',
        is_default BOOLEAN DEFAULT false,
        is_active BOOLEAN DEFAULT true,
        
        business_name VARCHAR(200) DEFAULT 'Ocean Soul Sparkles',
        business_address TEXT,
        business_phone VARCHAR(50),
        business_email VARCHAR(100),
        business_website VARCHAR(100),
        business_abn VARCHAR(50),
        
        show_logo BOOLEAN DEFAULT true,
        logo_position VARCHAR(20) DEFAULT 'center',
        header_color VARCHAR(7) DEFAULT '#667eea',
        text_color VARCHAR(7) DEFAULT '#333333',
        font_family VARCHAR(50) DEFAULT 'Arial',
        font_size INTEGER DEFAULT 12,
        
        show_customer_details BOOLEAN DEFAULT true,
        show_service_details BOOLEAN DEFAULT true,
        show_artist_details BOOLEAN DEFAULT true,
        show_payment_details BOOLEAN DEFAULT true,
        show_booking_notes BOOLEAN DEFAULT false,
        show_terms_conditions BOOLEAN DEFAULT true,
        
        footer_message TEXT DEFAULT 'Thank you for choosing Ocean Soul Sparkles!',
        show_social_media BOOLEAN DEFAULT false,
        social_media_links JSONB,
        custom_fields JSONB DEFAULT '[]',
        
        created_by UUID,
        updated_by UUID,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `;
    
    const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableSQL });
    if (createError) {
      console.error('❌ Error creating table:', createError);
      return;
    }
    
    console.log('✅ Receipt templates table created');
    
    // Check if default templates exist
    const { data: existingTemplates } = await supabase
      .from('receipt_templates')
      .select('name')
      .limit(1);
    
    if (!existingTemplates || existingTemplates.length === 0) {
      console.log('📄 Inserting default templates...');
      
      const defaultTemplates = [
        {
          name: 'Standard Receipt',
          description: 'Default receipt template with all standard information',
          template_type: 'standard',
          is_default: true,
          is_active: true,
          business_name: 'Ocean Soul Sparkles',
          business_address: 'Australia',
          business_phone: '+61 XXX XXX XXX',
          business_email: '<EMAIL>',
          business_website: 'oceansoulsparkles.com.au',
          show_logo: true,
          logo_position: 'center',
          header_color: '#667eea',
          text_color: '#333333',
          font_family: 'Arial',
          font_size: 12,
          show_customer_details: true,
          show_service_details: true,
          show_artist_details: true,
          show_payment_details: true,
          show_booking_notes: false,
          show_terms_conditions: true,
          footer_message: 'Thank you for choosing Ocean Soul Sparkles! We hope you love your new look!',
          show_social_media: false
        },
        {
          name: 'Compact Receipt',
          description: 'Minimal receipt template for quick transactions',
          template_type: 'compact',
          is_default: false,
          is_active: true,
          business_name: 'Ocean Soul Sparkles',
          business_address: 'Australia',
          business_phone: '+61 XXX XXX XXX',
          business_email: '<EMAIL>',
          business_website: 'oceansoulsparkles.com.au',
          show_logo: false,
          logo_position: 'left',
          header_color: '#667eea',
          text_color: '#333333',
          font_family: 'Arial',
          font_size: 10,
          show_customer_details: true,
          show_service_details: true,
          show_artist_details: false,
          show_payment_details: true,
          show_booking_notes: false,
          show_terms_conditions: false,
          footer_message: 'Thank you!',
          show_social_media: false
        }
      ];
      
      const { error: insertError } = await supabase
        .from('receipt_templates')
        .insert(defaultTemplates);
      
      if (insertError) {
        console.error('❌ Error inserting templates:', insertError);
      } else {
        console.log('✅ Default templates inserted');
      }
    }
    
    // Check final state
    const { data: templates, error: fetchError } = await supabase
      .from('receipt_templates')
      .select('id, name, template_type, is_default')
      .eq('is_active', true);
      
    if (fetchError) {
      console.error('❌ Error fetching templates:', fetchError);
    } else {
      console.log('📄 Available receipt templates:', templates?.length || 0);
      templates?.forEach(t => console.log(`  - ${t.name} (${t.template_type})${t.is_default ? ' [DEFAULT]' : ''}`));
    }
    
    console.log('🎯 Receipt customization system is ready!');
    
  } catch (error) {
    console.error('❌ Setup error:', error);
  }
}

setupReceiptTemplates();
