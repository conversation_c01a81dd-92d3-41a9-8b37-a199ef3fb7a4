"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertMerchantCustomAttributeRequest = exports.BulkUpsertMerchantCustomAttributesRequest = exports.BulkDeleteMerchantCustomAttributesRequest = void 0;
var BulkDeleteMerchantCustomAttributesRequest_1 = require("./BulkDeleteMerchantCustomAttributesRequest");
Object.defineProperty(exports, "BulkDeleteMerchantCustomAttributesRequest", { enumerable: true, get: function () { return BulkDeleteMerchantCustomAttributesRequest_1.BulkDeleteMerchantCustomAttributesRequest; } });
var BulkUpsertMerchantCustomAttributesRequest_1 = require("./BulkUpsertMerchantCustomAttributesRequest");
Object.defineProperty(exports, "BulkUpsertMerchantCustomAttributesRequest", { enumerable: true, get: function () { return BulkUpsertMerchantCustomAttributesRequest_1.BulkUpsertMerchantCustomAttributesRequest; } });
var UpsertMerchantCustomAttributeRequest_1 = require("./UpsertMerchantCustomAttributeRequest");
Object.defineProperty(exports, "UpsertMerchantCustomAttributeRequest", { enumerable: true, get: function () { return UpsertMerchantCustomAttributeRequest_1.UpsertMerchantCustomAttributeRequest; } });
