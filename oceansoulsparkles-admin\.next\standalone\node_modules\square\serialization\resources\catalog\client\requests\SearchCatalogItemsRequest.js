"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchCatalogItemsRequest = void 0;
const core = __importStar(require("../../../../../core"));
const SearchCatalogItemsRequestStockLevel_1 = require("../../../../types/SearchCatalogItemsRequestStockLevel");
const SortOrder_1 = require("../../../../types/SortOrder");
const CatalogItemProductType_1 = require("../../../../types/CatalogItemProductType");
const CustomAttributeFilter_1 = require("../../../../types/CustomAttributeFilter");
const ArchivedState_1 = require("../../../../types/ArchivedState");
exports.SearchCatalogItemsRequest = core.serialization.object({
    textFilter: core.serialization.property("text_filter", core.serialization.string().optional()),
    categoryIds: core.serialization.property("category_ids", core.serialization.list(core.serialization.string()).optional()),
    stockLevels: core.serialization.property("stock_levels", core.serialization.list(SearchCatalogItemsRequestStockLevel_1.SearchCatalogItemsRequestStockLevel).optional()),
    enabledLocationIds: core.serialization.property("enabled_location_ids", core.serialization.list(core.serialization.string()).optional()),
    cursor: core.serialization.string().optional(),
    limit: core.serialization.number().optional(),
    sortOrder: core.serialization.property("sort_order", SortOrder_1.SortOrder.optional()),
    productTypes: core.serialization.property("product_types", core.serialization.list(CatalogItemProductType_1.CatalogItemProductType).optional()),
    customAttributeFilters: core.serialization.property("custom_attribute_filters", core.serialization.list(CustomAttributeFilter_1.CustomAttributeFilter).optional()),
    archivedState: core.serialization.property("archived_state", ArchivedState_1.ArchivedState.optional()),
});
