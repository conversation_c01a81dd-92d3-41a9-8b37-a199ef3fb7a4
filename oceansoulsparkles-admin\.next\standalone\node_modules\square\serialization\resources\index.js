"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.webhooks = exports.terminal = exports.merchants = exports.devices = exports.vendors = exports.team = exports.teamMembers = exports.subscriptions = exports.snippets = exports.refunds = exports.payments = exports.orders = exports.checkout = exports.loyalty = exports.locations = exports.labor = exports.invoices = exports.giftCards = exports.events = exports.disputes = exports.customers = exports.catalog = exports.cards = exports.bookings = exports.applePay = exports.oAuth = exports.mobile = exports.v1Transactions = void 0;
exports.v1Transactions = __importStar(require("./v1Transactions"));
exports.mobile = __importStar(require("./mobile"));
__exportStar(require("./mobile/client/requests"), exports);
exports.oAuth = __importStar(require("./oAuth"));
__exportStar(require("./oAuth/client/requests"), exports);
__exportStar(require("./v1Transactions/client/requests"), exports);
exports.applePay = __importStar(require("./applePay"));
__exportStar(require("./applePay/client/requests"), exports);
exports.bookings = __importStar(require("./bookings"));
__exportStar(require("./bookings/client/requests"), exports);
exports.cards = __importStar(require("./cards"));
__exportStar(require("./cards/client/requests"), exports);
exports.catalog = __importStar(require("./catalog"));
__exportStar(require("./catalog/client/requests"), exports);
exports.customers = __importStar(require("./customers"));
__exportStar(require("./customers/client/requests"), exports);
exports.disputes = __importStar(require("./disputes"));
__exportStar(require("./disputes/client/requests"), exports);
exports.events = __importStar(require("./events"));
__exportStar(require("./events/client/requests"), exports);
exports.giftCards = __importStar(require("./giftCards"));
__exportStar(require("./giftCards/client/requests"), exports);
exports.invoices = __importStar(require("./invoices"));
__exportStar(require("./invoices/client/requests"), exports);
exports.labor = __importStar(require("./labor"));
__exportStar(require("./labor/client/requests"), exports);
exports.locations = __importStar(require("./locations"));
__exportStar(require("./locations/client/requests"), exports);
exports.loyalty = __importStar(require("./loyalty"));
__exportStar(require("./loyalty/client/requests"), exports);
exports.checkout = __importStar(require("./checkout"));
__exportStar(require("./checkout/client/requests"), exports);
exports.orders = __importStar(require("./orders"));
__exportStar(require("./orders/client/requests"), exports);
exports.payments = __importStar(require("./payments"));
__exportStar(require("./payments/client/requests"), exports);
exports.refunds = __importStar(require("./refunds"));
__exportStar(require("./refunds/client/requests"), exports);
exports.snippets = __importStar(require("./snippets"));
__exportStar(require("./snippets/client/requests"), exports);
exports.subscriptions = __importStar(require("./subscriptions"));
__exportStar(require("./subscriptions/client/requests"), exports);
exports.teamMembers = __importStar(require("./teamMembers"));
__exportStar(require("./teamMembers/client/requests"), exports);
exports.team = __importStar(require("./team"));
__exportStar(require("./team/client/requests"), exports);
exports.vendors = __importStar(require("./vendors"));
__exportStar(require("./vendors/client/requests"), exports);
exports.devices = __importStar(require("./devices"));
exports.merchants = __importStar(require("./merchants"));
exports.terminal = __importStar(require("./terminal"));
exports.webhooks = __importStar(require("./webhooks"));
