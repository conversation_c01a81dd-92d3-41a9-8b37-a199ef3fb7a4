"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertBookingCustomAttributeRequest = exports.BulkUpsertBookingCustomAttributesRequest = exports.BulkDeleteBookingCustomAttributesRequest = void 0;
var BulkDeleteBookingCustomAttributesRequest_1 = require("./BulkDeleteBookingCustomAttributesRequest");
Object.defineProperty(exports, "BulkDeleteBookingCustomAttributesRequest", { enumerable: true, get: function () { return BulkDeleteBookingCustomAttributesRequest_1.BulkDeleteBookingCustomAttributesRequest; } });
var BulkUpsertBookingCustomAttributesRequest_1 = require("./BulkUpsertBookingCustomAttributesRequest");
Object.defineProperty(exports, "BulkUpsertBookingCustomAttributesRequest", { enumerable: true, get: function () { return BulkUpsertBookingCustomAttributesRequest_1.BulkUpsertBookingCustomAttributesRequest; } });
var UpsertBookingCustomAttributeRequest_1 = require("./UpsertBookingCustomAttributeRequest");
Object.defineProperty(exports, "UpsertBookingCustomAttributeRequest", { enumerable: true, get: function () { return UpsertBookingCustomAttributeRequest_1.UpsertBookingCustomAttributeRequest; } });
