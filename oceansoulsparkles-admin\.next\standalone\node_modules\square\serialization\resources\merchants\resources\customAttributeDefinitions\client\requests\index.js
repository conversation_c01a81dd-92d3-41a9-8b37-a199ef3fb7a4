"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateMerchantCustomAttributeDefinitionRequest = exports.CreateMerchantCustomAttributeDefinitionRequest = void 0;
var CreateMerchantCustomAttributeDefinitionRequest_1 = require("./CreateMerchantCustomAttributeDefinitionRequest");
Object.defineProperty(exports, "CreateMerchantCustomAttributeDefinitionRequest", { enumerable: true, get: function () { return CreateMerchantCustomAttributeDefinitionRequest_1.CreateMerchantCustomAttributeDefinitionRequest; } });
var UpdateMerchantCustomAttributeDefinitionRequest_1 = require("./UpdateMerchantCustomAttributeDefinitionRequest");
Object.defineProperty(exports, "UpdateMerchantCustomAttributeDefinitionRequest", { enumerable: true, get: function () { return UpdateMerchantCustomAttributeDefinitionRequest_1.UpdateMerchantCustomAttributeDefinitionRequest; } });
