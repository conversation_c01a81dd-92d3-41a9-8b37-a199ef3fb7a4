"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.lazyObject = exports.lazy = void 0;
var lazy_1 = require("./lazy");
Object.defineProperty(exports, "lazy", { enumerable: true, get: function () { return lazy_1.lazy; } });
var lazyObject_1 = require("./lazyObject");
Object.defineProperty(exports, "lazyObject", { enumerable: true, get: function () { return lazyObject_1.lazyObject; } });
