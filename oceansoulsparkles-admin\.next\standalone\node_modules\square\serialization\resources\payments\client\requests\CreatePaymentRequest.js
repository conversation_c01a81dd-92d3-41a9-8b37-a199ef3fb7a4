"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePaymentRequest = void 0;
const core = __importStar(require("../../../../../core"));
const Money_1 = require("../../../../types/Money");
const Address_1 = require("../../../../types/Address");
const CashPaymentDetails_1 = require("../../../../types/CashPaymentDetails");
const ExternalPaymentDetails_1 = require("../../../../types/ExternalPaymentDetails");
const CustomerDetails_1 = require("../../../../types/CustomerDetails");
const OfflinePaymentDetails_1 = require("../../../../types/OfflinePaymentDetails");
exports.CreatePaymentRequest = core.serialization.object({
    sourceId: core.serialization.property("source_id", core.serialization.string()),
    idempotencyKey: core.serialization.property("idempotency_key", core.serialization.string()),
    amountMoney: core.serialization.property("amount_money", Money_1.Money.optional()),
    tipMoney: core.serialization.property("tip_money", Money_1.Money.optional()),
    appFeeMoney: core.serialization.property("app_fee_money", Money_1.Money.optional()),
    delayDuration: core.serialization.property("delay_duration", core.serialization.string().optional()),
    delayAction: core.serialization.property("delay_action", core.serialization.string().optional()),
    autocomplete: core.serialization.boolean().optional(),
    orderId: core.serialization.property("order_id", core.serialization.string().optional()),
    customerId: core.serialization.property("customer_id", core.serialization.string().optional()),
    locationId: core.serialization.property("location_id", core.serialization.string().optional()),
    teamMemberId: core.serialization.property("team_member_id", core.serialization.string().optional()),
    referenceId: core.serialization.property("reference_id", core.serialization.string().optional()),
    verificationToken: core.serialization.property("verification_token", core.serialization.string().optional()),
    acceptPartialAuthorization: core.serialization.property("accept_partial_authorization", core.serialization.boolean().optional()),
    buyerEmailAddress: core.serialization.property("buyer_email_address", core.serialization.string().optional()),
    buyerPhoneNumber: core.serialization.property("buyer_phone_number", core.serialization.string().optional()),
    billingAddress: core.serialization.property("billing_address", Address_1.Address.optional()),
    shippingAddress: core.serialization.property("shipping_address", Address_1.Address.optional()),
    note: core.serialization.string().optional(),
    statementDescriptionIdentifier: core.serialization.property("statement_description_identifier", core.serialization.string().optional()),
    cashDetails: core.serialization.property("cash_details", CashPaymentDetails_1.CashPaymentDetails.optional()),
    externalDetails: core.serialization.property("external_details", ExternalPaymentDetails_1.ExternalPaymentDetails.optional()),
    customerDetails: core.serialization.property("customer_details", CustomerDetails_1.CustomerDetails.optional()),
    offlinePaymentDetails: core.serialization.property("offline_payment_details", OfflinePaymentDetails_1.OfflinePaymentDetails.optional()),
});
