import { useState, useEffect } from 'react'
import styles from '@/styles/admin/ReceiptCustomizer.module.css'

/**
 * Receipt Customizer Component
 * Allows customization of receipt templates with live preview
 */
export default function ReceiptCustomizer({ onTemplateSelect, selectedTemplate, showPreview = true }) {
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [previewData, setPreviewData] = useState(null)

  useEffect(() => {
    loadTemplates()
    generatePreviewData()
  }, [])

  const loadTemplates = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/receipts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      })

      if (!response.ok) {
        throw new Error('Failed to load receipt templates')
      }

      const data = await response.json()
      setTemplates(data.templates || [])
      
      // Select default template if none selected
      if (!selectedTemplate && data.templates?.length > 0) {
        const defaultTemplate = data.templates.find(t => t.is_default) || data.templates[0]
        onTemplateSelect?.(defaultTemplate)
      }
    } catch (err) {
      console.error('Error loading templates:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const generatePreviewData = () => {
    setPreviewData({
      receipt_number: `OSS-${Date.now()}`,
      customer_name: 'Sarah Johnson',
      customer_email: '<EMAIL>',
      customer_phone: '+61 400 123 456',
      service_name: 'Festival Face Paint',
      tier_name: 'Premium',
      artist_name: 'Emma Wilson',
      start_time: new Date().toISOString(),
      duration: 90,
      total_amount: 120.00,
      tip_amount: 18.00,
      payment_method: 'Card',
      notes: 'Customer requested glitter accents'
    })
  }

  const handleTemplateSelect = (template) => {
    onTemplateSelect?.(template)
  }

  const getTemplateTypeIcon = (type) => {
    switch (type) {
      case 'compact': return '📄'
      case 'detailed': return '📋'
      default: return '🧾'
    }
  }

  const getTemplateTypeDescription = (type) => {
    switch (type) {
      case 'compact': return 'Minimal receipt with essential information only'
      case 'detailed': return 'Comprehensive receipt with all available details'
      default: return 'Standard receipt with balanced information'
    }
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading receipt templates...</p>
      </div>
    )
  }

  if (error) {
    return (
      <div className={styles.error}>
        <p>Error loading templates: {error}</p>
        <button onClick={loadTemplates} className={styles.retryBtn}>
          Try Again
        </button>
      </div>
    )
  }

  return (
    <div className={styles.receiptCustomizer}>
      <div className={styles.templatesSection}>
        <h3>Receipt Templates</h3>
        <p className={styles.description}>
          Choose a receipt template for your transactions. You can customize these templates in the admin settings.
        </p>
        
        <div className={styles.templateGrid}>
          {templates.map((template) => (
            <div
              key={template.id}
              className={`${styles.templateCard} ${
                selectedTemplate?.id === template.id ? styles.selected : ''
              }`}
              onClick={() => handleTemplateSelect(template)}
            >
              <div className={styles.templateHeader}>
                <span className={styles.templateIcon}>
                  {getTemplateTypeIcon(template.template_type)}
                </span>
                <div className={styles.templateInfo}>
                  <h4 className={styles.templateName}>{template.name}</h4>
                  {template.is_default && (
                    <span className={styles.defaultBadge}>Default</span>
                  )}
                </div>
              </div>
              
              <p className={styles.templateDescription}>
                {template.description || getTemplateTypeDescription(template.template_type)}
              </p>
              
              <div className={styles.templateFeatures}>
                <div className={styles.featureList}>
                  {template.show_customer_details && (
                    <span className={styles.feature}>👤 Customer Details</span>
                  )}
                  {template.show_service_details && (
                    <span className={styles.feature}>🎨 Service Info</span>
                  )}
                  {template.show_artist_details && (
                    <span className={styles.feature}>✨ Artist Info</span>
                  )}
                  {template.show_payment_details && (
                    <span className={styles.feature}>💳 Payment Details</span>
                  )}
                </div>
              </div>
              
              <div className={styles.templateMeta}>
                <span className={styles.templateType}>
                  {template.template_type.charAt(0).toUpperCase() + template.template_type.slice(1)}
                </span>
                <span className={styles.businessName}>
                  {template.business_name}
                </span>
              </div>
            </div>
          ))}
        </div>

        {templates.length === 0 && (
          <div className={styles.noTemplates}>
            <p>No receipt templates found.</p>
            <p>Default templates will be created automatically.</p>
          </div>
        )}
      </div>

      {showPreview && selectedTemplate && previewData && (
        <div className={styles.previewSection}>
          <h3>Receipt Preview</h3>
          <div className={styles.previewContainer}>
            <ReceiptPreview 
              template={selectedTemplate} 
              data={previewData} 
            />
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Receipt Preview Component
 * Shows a live preview of how the receipt will look
 */
function ReceiptPreview({ template, data }) {
  const [previewHtml, setPreviewHtml] = useState('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    generatePreview()
  }, [template, data])

  const generatePreview = async () => {
    try {
      setLoading(true)
      
      // Generate preview HTML using the receipt generator
      const response = await fetch('/api/admin/receipts/preview', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({
          templateId: template.id,
          bookingData: data
        })
      })

      if (!response.ok) {
        throw new Error('Failed to generate preview')
      }

      const result = await response.json()
      setPreviewHtml(result.html || '')
    } catch (error) {
      console.error('Error generating preview:', error)
      setPreviewHtml('<p>Preview not available</p>')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className={styles.previewLoading}>
        <div className={styles.loadingSpinner}></div>
        <p>Generating preview...</p>
      </div>
    )
  }

  return (
    <div className={styles.receiptPreview}>
      <div className={styles.previewHeader}>
        <span className={styles.previewLabel}>Preview</span>
        <button 
          onClick={generatePreview}
          className={styles.refreshBtn}
          title="Refresh Preview"
        >
          🔄
        </button>
      </div>
      <div 
        className={styles.previewContent}
        dangerouslySetInnerHTML={{ __html: previewHtml }}
      />
    </div>
  )
}
