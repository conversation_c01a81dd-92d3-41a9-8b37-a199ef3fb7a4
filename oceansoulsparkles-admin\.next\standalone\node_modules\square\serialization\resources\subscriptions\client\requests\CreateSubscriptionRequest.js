"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSubscriptionRequest = void 0;
const core = __importStar(require("../../../../../core"));
const Money_1 = require("../../../../types/Money");
const SubscriptionSource_1 = require("../../../../types/SubscriptionSource");
const Phase_1 = require("../../../../types/Phase");
exports.CreateSubscriptionRequest = core.serialization.object({
    idempotencyKey: core.serialization.property("idempotency_key", core.serialization.string().optional()),
    locationId: core.serialization.property("location_id", core.serialization.string()),
    planVariationId: core.serialization.property("plan_variation_id", core.serialization.string().optional()),
    customerId: core.serialization.property("customer_id", core.serialization.string()),
    startDate: core.serialization.property("start_date", core.serialization.string().optional()),
    canceledDate: core.serialization.property("canceled_date", core.serialization.string().optional()),
    taxPercentage: core.serialization.property("tax_percentage", core.serialization.string().optional()),
    priceOverrideMoney: core.serialization.property("price_override_money", Money_1.Money.optional()),
    cardId: core.serialization.property("card_id", core.serialization.string().optional()),
    timezone: core.serialization.string().optional(),
    source: SubscriptionSource_1.SubscriptionSource.optional(),
    monthlyBillingAnchorDate: core.serialization.property("monthly_billing_anchor_date", core.serialization.number().optional()),
    phases: core.serialization.list(Phase_1.Phase).optional(),
});
