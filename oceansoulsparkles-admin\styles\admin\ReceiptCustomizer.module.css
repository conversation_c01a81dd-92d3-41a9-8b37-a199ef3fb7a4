/* Receipt Customizer Styles */
.receiptCustomizer {
  display: flex;
  gap: 30px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  min-height: 600px;
}

.templatesSection {
  flex: 1;
  min-width: 400px;
}

.templatesSection h3 {
  margin: 0 0 8px 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
}

.description {
  color: #64748b;
  margin: 0 0 20px 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.templateGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.templateCard {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.templateCard:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
}

.templateCard.selected {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea08 0%, #764ba208 100%);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

.templateHeader {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
}

.templateIcon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.templateInfo {
  flex: 1;
}

.templateName {
  margin: 0 0 4px 0;
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
}

.defaultBadge {
  background: #10b981;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.templateDescription {
  color: #64748b;
  margin: 0 0 16px 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.templateFeatures {
  margin-bottom: 16px;
}

.featureList {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.feature {
  background: #f1f5f9;
  color: #475569;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.templateMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
  font-size: 0.85rem;
}

.templateType {
  background: #667eea;
  color: white;
  padding: 3px 8px;
  border-radius: 6px;
  font-weight: 500;
  text-transform: capitalize;
}

.businessName {
  color: #64748b;
  font-weight: 500;
}

.previewSection {
  flex: 1;
  min-width: 400px;
  max-width: 500px;
}

.previewSection h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
}

.previewContainer {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.receiptPreview {
  height: 100%;
}

.previewHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.previewLabel {
  font-weight: 600;
  color: #475569;
  font-size: 0.9rem;
}

.refreshBtn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.refreshBtn:hover {
  background: #e2e8f0;
}

.previewContent {
  padding: 20px;
  max-height: 600px;
  overflow-y: auto;
  font-family: Arial, sans-serif;
}

/* Loading States */
.loading, .previewLoading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #64748b;
}

.loadingSpinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error {
  text-align: center;
  padding: 40px;
  color: #dc2626;
}

.retryBtn {
  background: #667eea;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  margin-top: 12px;
  transition: background-color 0.2s ease;
}

.retryBtn:hover {
  background: #5a67d8;
}

/* No Templates State */
.noTemplates {
  text-align: center;
  padding: 40px;
  color: #64748b;
  background: white;
  border: 2px dashed #e2e8f0;
  border-radius: 12px;
}

.noTemplates p {
  margin: 0 0 8px 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .receiptCustomizer {
    flex-direction: column;
    gap: 20px;
  }
  
  .templatesSection,
  .previewSection {
    min-width: auto;
    max-width: none;
  }
  
  .templateGrid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .receiptCustomizer {
    padding: 16px;
  }
  
  .templateGrid {
    grid-template-columns: 1fr;
  }
  
  .templateCard {
    padding: 16px;
  }
  
  .templateHeader {
    gap: 8px;
  }
  
  .featureList {
    gap: 4px;
  }
  
  .feature {
    font-size: 0.75rem;
    padding: 3px 6px;
  }
}

/* Print Styles for Receipt Preview */
@media print {
  .receiptCustomizer {
    display: block;
  }
  
  .templatesSection {
    display: none;
  }
  
  .previewSection {
    max-width: none;
  }
  
  .previewHeader {
    display: none;
  }
  
  .previewContent {
    padding: 0;
    max-height: none;
    overflow: visible;
  }
}
