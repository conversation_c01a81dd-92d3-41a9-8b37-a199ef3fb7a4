"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertOrderCustomAttributeRequest = exports.BulkUpsertOrderCustomAttributesRequest = exports.BulkDeleteOrderCustomAttributesRequest = void 0;
var BulkDeleteOrderCustomAttributesRequest_1 = require("./BulkDeleteOrderCustomAttributesRequest");
Object.defineProperty(exports, "BulkDeleteOrderCustomAttributesRequest", { enumerable: true, get: function () { return BulkDeleteOrderCustomAttributesRequest_1.BulkDeleteOrderCustomAttributesRequest; } });
var BulkUpsertOrderCustomAttributesRequest_1 = require("./BulkUpsertOrderCustomAttributesRequest");
Object.defineProperty(exports, "BulkUpsertOrderCustomAttributesRequest", { enumerable: true, get: function () { return BulkUpsertOrderCustomAttributesRequest_1.BulkUpsertOrderCustomAttributesRequest; } });
var UpsertOrderCustomAttributeRequest_1 = require("./UpsertOrderCustomAttributeRequest");
Object.defineProperty(exports, "UpsertOrderCustomAttributeRequest", { enumerable: true, get: function () { return UpsertOrderCustomAttributeRequest_1.UpsertOrderCustomAttributeRequest; } });
