"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpsertLocationCustomAttributeRequest = exports.BulkUpsertLocationCustomAttributesRequest = exports.BulkDeleteLocationCustomAttributesRequest = void 0;
var BulkDeleteLocationCustomAttributesRequest_1 = require("./BulkDeleteLocationCustomAttributesRequest");
Object.defineProperty(exports, "BulkDeleteLocationCustomAttributesRequest", { enumerable: true, get: function () { return BulkDeleteLocationCustomAttributesRequest_1.BulkDeleteLocationCustomAttributesRequest; } });
var BulkUpsertLocationCustomAttributesRequest_1 = require("./BulkUpsertLocationCustomAttributesRequest");
Object.defineProperty(exports, "BulkUpsertLocationCustomAttributesRequest", { enumerable: true, get: function () { return BulkUpsertLocationCustomAttributesRequest_1.BulkUpsertLocationCustomAttributesRequest; } });
var UpsertLocationCustomAttributeRequest_1 = require("./UpsertLocationCustomAttributeRequest");
Object.defineProperty(exports, "UpsertLocationCustomAttributeRequest", { enumerable: true, get: function () { return UpsertLocationCustomAttributeRequest_1.UpsertLocationCustomAttributeRequest; } });
