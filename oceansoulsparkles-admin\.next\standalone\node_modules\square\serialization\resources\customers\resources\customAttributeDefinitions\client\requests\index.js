"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BatchUpsertCustomerCustomAttributesRequest = exports.UpdateCustomerCustomAttributeDefinitionRequest = exports.CreateCustomerCustomAttributeDefinitionRequest = void 0;
var CreateCustomerCustomAttributeDefinitionRequest_1 = require("./CreateCustomerCustomAttributeDefinitionRequest");
Object.defineProperty(exports, "CreateCustomerCustomAttributeDefinitionRequest", { enumerable: true, get: function () { return CreateCustomerCustomAttributeDefinitionRequest_1.CreateCustomerCustomAttributeDefinitionRequest; } });
var UpdateCustomerCustomAttributeDefinitionRequest_1 = require("./UpdateCustomerCustomAttributeDefinitionRequest");
Object.defineProperty(exports, "UpdateCustomerCustomAttributeDefinitionRequest", { enumerable: true, get: function () { return UpdateCustomerCustomAttributeDefinitionRequest_1.UpdateCustomerCustomAttributeDefinitionRequest; } });
var BatchUpsertCustomerCustomAttributesRequest_1 = require("./BatchUpsertCustomerCustomAttributesRequest");
Object.defineProperty(exports, "BatchUpsertCustomerCustomAttributesRequest", { enumerable: true, get: function () { return BatchUpsertCustomerCustomAttributesRequest_1.BatchUpsertCustomerCustomAttributesRequest; } });
