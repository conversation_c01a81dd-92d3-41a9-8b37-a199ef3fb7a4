"use strict";
/**
 * This file was auto-generated by Fern from our API Definition.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.RefundPaymentRequest = void 0;
const core = __importStar(require("../../../../../core"));
const Money_1 = require("../../../../types/Money");
const DestinationDetailsCashRefundDetails_1 = require("../../../../types/DestinationDetailsCashRefundDetails");
const DestinationDetailsExternalRefundDetails_1 = require("../../../../types/DestinationDetailsExternalRefundDetails");
exports.RefundPaymentRequest = core.serialization.object({
    idempotencyKey: core.serialization.property("idempotency_key", core.serialization.string()),
    amountMoney: core.serialization.property("amount_money", Money_1.Money),
    appFeeMoney: core.serialization.property("app_fee_money", Money_1.Money.optional()),
    paymentId: core.serialization.property("payment_id", core.serialization.string().optionalNullable()),
    destinationId: core.serialization.property("destination_id", core.serialization.string().optionalNullable()),
    unlinked: core.serialization.boolean().optionalNullable(),
    locationId: core.serialization.property("location_id", core.serialization.string().optionalNullable()),
    customerId: core.serialization.property("customer_id", core.serialization.string().optionalNullable()),
    reason: core.serialization.string().optionalNullable(),
    paymentVersionToken: core.serialization.property("payment_version_token", core.serialization.string().optionalNullable()),
    teamMemberId: core.serialization.property("team_member_id", core.serialization.string().optionalNullable()),
    cashDetails: core.serialization.property("cash_details", DestinationDetailsCashRefundDetails_1.DestinationDetailsCashRefundDetails.optional()),
    externalDetails: core.serialization.property("external_details", DestinationDetailsExternalRefundDetails_1.DestinationDetailsExternalRefundDetails.optional()),
});
