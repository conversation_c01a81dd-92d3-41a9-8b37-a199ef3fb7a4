"use strict";
/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SquareError = void 0;
const json_1 = require("../core/json");
const api_1 = require("../api");
const fallbackError = {
    category: "V1_ERROR",
    code: "Unknown",
};
class SquareError extends Error {
    constructor({ message, statusCode, body }) {
        var _a, _b;
        super(buildMessage({ message, statusCode, body }));
        Object.setPrototypeOf(this, SquareError.prototype);
        if (statusCode != null) {
            this.statusCode = statusCode;
        }
        this.body = body;
        if (body != null && typeof body === "object") {
            if ("errors" in body) {
                this.errors = (_a = body.errors) !== null && _a !== void 0 ? _a : [fallbackError];
            }
            else {
                const v1Error = body;
                this.errors = [
                    {
                        category: SquareError.ErrorCategory.V1Error,
                        code: (_b = v1Error.type) !== null && _b !== void 0 ? _b : SquareError.ErrorCode.Unknown,
                        detail: v1Error.message,
                        field: v1Error.field,
                    }
                ];
            }
        }
        else {
            this.errors = [fallbackError];
        }
    }
}
exports.SquareError = SquareError;
function buildMessage({ message, statusCode, body, }) {
    let lines = [];
    if (message != null) {
        lines.push(message);
    }
    if (statusCode != null) {
        lines.push(`Status code: ${statusCode.toString()}`);
    }
    if (body != null) {
        lines.push(`Body: ${(0, json_1.toJson)(body, undefined, 2)}`);
    }
    return lines.join("\n");
}
(function (SquareError) {
    SquareError.ErrorCategory = Object.assign(Object.assign({}, api_1.ErrorCategory), { V1Error: "V1_ERROR" });
    SquareError.ErrorCode = Object.assign(Object.assign({}, api_1.ErrorCode), { Unknown: "Unknown" });
})(SquareError || (exports.SquareError = SquareError = {}));
